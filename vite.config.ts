import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    outDir: resolve(__dirname, 'dist'),
    // Do not empty the outDir on rebuilds in watch mode
    emptyOutDir: true, 
    rollupOptions: {
      // Define the entry points for the extension
      input: {
        background: resolve(__dirname, 'src/background/background.ts'),
        content: resolve(__dirname, 'src/content/main.tsx'),
      },
      output: {
        // Define the output file names
        entryFileNames: '[name].js',
        // Output format for browser compatibility
        format: 'iife',
      },
    },
  },
});