import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import { crx } from "@crxjs/vite-plugin";
import manifest from "./manifest.json";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), crx({ manifest })],

  // Ensure proper resolution for browser extension context
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});
