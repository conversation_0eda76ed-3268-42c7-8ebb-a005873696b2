import React from 'react';
import <PERSON>actD<PERSON> from 'react-dom/client';
import AutocompleteInjector from './AutocompleteInjector';
import './autocomplete.css';

const inputs = document.querySelectorAll<HTMLInputElement | HTMLTextAreaElement>(
  'input[type="text"], input:not([type]), textarea'
);

inputs.forEach(input => {
  // Don't inject if it's already done
  if (input.closest('.autocomplete-container')) return;

  // Create a container div that will become the React root
  const container = document.createElement('div');
  input.parentNode!.insertBefore(container, input);
  
  // Render our React component into the container
  const root = ReactDOM.createRoot(container);
  // We pass the original input element as a prop to our component
  root.render(
    <React.StrictMode>
      <AutocompleteInjector targetInput={input} />
    </React.StrictMode>
  );
});