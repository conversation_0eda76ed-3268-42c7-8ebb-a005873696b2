/* Autocomplete injector styles */
.autocomplete-container {
  position: relative;
  display: inline-block;
}

.suggestion-display {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow: hidden;
  padding: inherit;
  margin: inherit;
  border: inherit;
  font: inherit;
  line-height: inherit;
  box-sizing: inherit;
}

.ghost-text {
  color: #999;
  opacity: 0.6;
}

/* Make the real input transparent but keep the cursor visible */
.real-input-injected {
  background: transparent !important;
  color: transparent !important;
  caret-color: #000 !important;
  position: relative;
  z-index: 2;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .ghost-text {
    color: #666;
    opacity: 0.7;
  }
  
  .real-input-injected {
    caret-color: #fff !important;
  }
}

/* Ensure proper layering and positioning */
.autocomplete-container input,
.autocomplete-container textarea {
  position: relative;
  z-index: 2;
}

.autocomplete-container .suggestion-display {
  z-index: 1;
}
