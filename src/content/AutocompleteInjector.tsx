import React, { useState, useEffect, useRef } from 'react';
import type { CSSProperties } from 'react';

// Define the component's props
interface AutocompleteInjectorProps {
  targetInput: HTMLInputElement | HTMLTextAreaElement;
}

const AutocompleteInjector: React.FC<AutocompleteInjectorProps> = ({ targetInput }) => {
  const [userInput, setUserInput] = useState(targetInput.value);
  const [suggestion, setSuggestion] = useState('');
  const [activeStyles, setActiveStyles] = useState<CSSProperties>({});
  const debounceTimer = useRef<number | null>(null);

  // This effect copies the styles from the original input to our wrapper
  useEffect(() => {
    const computedStyle = window.getComputedStyle(targetInput);
    const styles: CSSProperties = {};
    const propertiesToCopy: (keyof CSSStyleDeclaration)[] = [
        'font', 'padding', 'border', 'width', 'height', 'boxSizing', 'lineHeight', 'borderRadius'
    ];
    propertiesToCopy.forEach(prop => {
        // Type assertion needed because CSSStyleDeclaration properties can be functions
        styles[prop as any] = computedStyle[prop as any];
    });
    setActiveStyles(styles);
    targetInput.classList.add('real-input-injected'); // Add class to make it transparent
  }, [targetInput]);


  // Effect for fetching suggestions
  useEffect(() => {
    const text = userInput;
    const wordCount = text.trim().split(/\s+/).filter(Boolean).length;

    const fetchSuggestion = () => {
      const pageContext = {
        title: document.title,
        h1: document.querySelector('h1')?.textContent ?? '',
      };
      chrome.runtime.sendMessage({ type: 'getSuggestion', text, pageContext }, (response) => {
        if (response?.suggestion) {
          setSuggestion(response.suggestion);
        }
      });
    };
    
    clearTimeout(debounceTimer.current!);

    if (wordCount >= 4 && text.endsWith(' ')) {
      fetchSuggestion();
    } else if (text.trim().length > 0) {
      debounceTimer.current = window.setTimeout(fetchSuggestion, 500);
    }

    // Cleanup function
    return () => clearTimeout(debounceTimer.current!);

  }, [userInput]);

  const handleInput = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setUserInput(e.target.value);
    setSuggestion(''); // Clear suggestion on new input
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const isSuggestionVisible = suggestion && suggestion.toLowerCase().startsWith(userInput.toLowerCase());
    const ghostText = isSuggestionVisible ? suggestion.slice(userInput.length) : '';
    const isCursorAtEnd = e.currentTarget.selectionStart === userInput.length;

    if ((e.key === 'Tab' || e.key === 'ArrowRight') && ghostText && isCursorAtEnd) {
      e.preventDefault();
      const newValue = userInput + ghostText;
      setUserInput(newValue);
      targetInput.value = newValue; // Directly update the real input's value
      setSuggestion('');

      // Dispatch an event to notify the page of the change
      targetInput.dispatchEvent(new Event('input', { bubbles: true }));
    }
  };

  const ghostText = suggestion && suggestion.toLowerCase().startsWith(userInput.toLowerCase()) 
    ? suggestion.slice(userInput.length) 
    : '';

  return (
    <div className="autocomplete-container" style={activeStyles}>
      <div className="suggestion-display">
        <span>{userInput}</span>
        <span className="ghost-text">{ghostText}</span>
      </div>
      {/* Clone the original input and attach our event handlers */}
      {React.cloneElement(targetInput as any, {
          onInput: handleInput,
          onKeyDown: handleKeyDown,
          value: userInput,
      })}
    </div>
  );
};

export default AutocompleteInjector;