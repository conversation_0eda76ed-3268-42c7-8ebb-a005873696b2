import React, { useState, useEffect, useRef } from 'react';
import type { CSSProperties } from 'react';

// Define the component's props
interface AutocompleteInjectorProps {
  targetInput: HTMLInputElement | HTMLTextAreaElement;
}

const AutocompleteInjector: React.FC<AutocompleteInjectorProps> = ({ targetInput }) => {
  const [userInput, setUserInput] = useState(targetInput.value);
  const [suggestion, setSuggestion] = useState('');
  const [activeStyles, setActiveStyles] = useState<CSSProperties>({});
  const debounceTimer = useRef<number | null>(null);

  // This effect copies the styles from the original input to our wrapper
  useEffect(() => {
    const computedStyle = window.getComputedStyle(targetInput);
    const styles: CSSProperties = {};
    const propertiesToCopy = [
        'font', 'padding', 'border', 'width', 'height', 'boxSizing', 'lineHeight', 'borderRadius'
    ] as const;

    propertiesToCopy.forEach(prop => {
        // Safely copy CSS properties with proper type handling
        const value = computedStyle.getPropertyValue(prop);
        if (value) {
          // Use index signature to safely assign CSS properties
          (styles as Record<string, string>)[prop] = value;
        }
    });
    setActiveStyles(styles);
    targetInput.classList.add('real-input-injected'); // Add class to make it transparent
  }, [targetInput]);


  // Effect for fetching suggestions
  useEffect(() => {
    const text = userInput;
    const wordCount = text.trim().split(/\s+/).filter(Boolean).length;

    const fetchSuggestion = () => {
      const pageContext = {
        title: document.title,
        h1: document.querySelector('h1')?.textContent ?? '',
      };
      chrome.runtime.sendMessage({ type: 'getSuggestion', text, pageContext }, (response) => {
        if (response?.suggestion) {
          setSuggestion(response.suggestion);
        }
      });
    };
    
    clearTimeout(debounceTimer.current!);

    if (wordCount >= 4 && text.endsWith(' ')) {
      fetchSuggestion();
    } else if (text.trim().length > 0) {
      debounceTimer.current = window.setTimeout(fetchSuggestion, 500);
    }

    // Cleanup function
    return () => clearTimeout(debounceTimer.current!);

  }, [userInput]);

  const handleInputChange = (e: React.FormEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const target = e.currentTarget;
    setUserInput(target.value);
    setSuggestion(''); // Clear suggestion on new input
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const isSuggestionVisible = suggestion && suggestion.toLowerCase().startsWith(userInput.toLowerCase());
    const ghostText = isSuggestionVisible ? suggestion.slice(userInput.length) : '';
    const isCursorAtEnd = e.currentTarget.selectionStart === userInput.length;

    if ((e.key === 'Tab' || e.key === 'ArrowRight') && ghostText && isCursorAtEnd) {
      e.preventDefault();
      const newValue = userInput + ghostText;
      setUserInput(newValue);
      targetInput.value = newValue; // Directly update the real input's value
      setSuggestion('');

      // Dispatch an event to notify the page of the change
      targetInput.dispatchEvent(new Event('input', { bubbles: true }));
    }
  };

  const ghostText = suggestion && suggestion.toLowerCase().startsWith(userInput.toLowerCase()) 
    ? suggestion.slice(userInput.length) 
    : '';

  return (
    <div className="autocomplete-container" style={activeStyles}>
      <div className="suggestion-display">
        <span>{userInput}</span>
        <span className="ghost-text">{ghostText}</span>
      </div>
      {/* Create a React element that mirrors the original input */}
      {targetInput.tagName.toLowerCase() === 'textarea' ? (
        <textarea
          onInput={handleInputChange}
          onKeyDown={handleKeyDown}
          value={userInput}
          style={{ background: 'transparent', color: 'transparent', caretColor: 'black' }}
        />
      ) : (
        <input
          type={(targetInput as HTMLInputElement).type || 'text'}
          onInput={handleInputChange}
          onKeyDown={handleKeyDown}
          value={userInput}
          style={{ background: 'transparent', color: 'transparent', caretColor: 'black' }}
        />
      )}
    </div>
  );
};

export default AutocompleteInjector;