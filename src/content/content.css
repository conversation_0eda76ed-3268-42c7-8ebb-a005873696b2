.autocomplete-container {
  position: relative;
  display: inline-block; /* Or match the original display */
}

/* This is the visual layer for the text and suggestion */
.suggestion-display {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  pointer-events: none; /* Allows clicks to go to the real input */
  white-space: pre-wrap; /* Allows text to wrap */
  color: transparent; /* Text is shown in the spans below */

  /* The component copies font, padding, border etc. from the real input */
}

.ghost-text {
  color: #999;
}

/* This is the original input, now made transparent and placed on top */
.real-input-injected {
  position: relative;
  z-index: 2;
  background: transparent !important;
  color: transparent !important;
  caret-color: black; /* Makes the blinking cursor visible again */
}