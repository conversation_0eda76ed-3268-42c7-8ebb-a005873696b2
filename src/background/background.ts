// Define interfaces for type safety
interface PageContext {
  title: string;
  h1: string;
}

interface MessagePayload {
  type: 'getSuggestion';
  text: string;
  pageContext: PageContext;
}

chrome.runtime.onMessage.addListener((request: MessagePayload, _sender, sendResponse) => {
  if (request.type === 'getSuggestion') {
    const YOUR_GEMINI_API_KEY = "YOUR_API_KEY_HERE"; // Store securely in production
    const { text, pageContext } = request;

    // Create a detailed prompt using the page context for better suggestions
    const prompt = `You are an intelligent autocomplete assistant helping users write text on a webpage.

Context:
- Page title: "${pageContext.title}"
- Main heading: "${pageContext.h1}"
- Current text: "${text}"

Please provide a natural continuation of the text that fits the context. Only return the continuation text, no explanations or quotes. Keep it concise and relevant to the page context.`;

    fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${YOUR_GEMINI_API_KEY}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        contents: [{ parts: [{ text: prompt }] }],
        generationConfig: {
            temperature: 0.4,
            maxOutputTokens: 64,
            stopSequences: [".", "\n"],
        },
      }),
    })
      .then(response => response.json())
      .then(data => {
        if (data.candidates && data.candidates[0]?.content.parts[0]?.text) {
          const completion = data.candidates[0].content.parts[0].text;
          const fullSuggestion = text + (!text.endsWith(' ') ? ' ' : '') + completion.trimEnd();
          sendResponse({ suggestion: fullSuggestion });
        } else {
          sendResponse({});
        }
      })
      .catch(error => {
        console.error('Error fetching from Gemini API:', error);
        sendResponse({}); // Send empty response on error
      });

    return true; // Indicates an asynchronous response
  }
});