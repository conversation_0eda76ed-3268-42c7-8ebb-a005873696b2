// Define interfaces for type safety
interface PageContext {
  title: string;
  h1: string;
}

interface MessagePayload {
  type: 'getSuggestion';
  text: string;
  pageContext: PageContext;
}

chrome.runtime.onMessage.addListener((request: MessagePayload, sender, sendResponse) => {
  if (request.type === 'getSuggestion') {
    const YOUR_GEMINI_API_KEY = "YOUR_API_KEY_HERE"; // Store securely in production
    const { text, pageContext } = request;

    const prompt = `You are an intelligent autocomplete assistant...`; // Same detailed prompt as the JS example

    fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${YOUR_GEMINI_API_KEY}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        contents: [{ parts: [{ text: prompt }] }],
        generationConfig: {
            temperature: 0.4,
            maxOutputTokens: 64,
            stopSequences: [".", "\n"],
        },
      }),
    })
      .then(response => response.json())
      .then(data => {
        if (data.candidates && data.candidates[0]?.content.parts[0]?.text) {
          const completion = data.candidates[0].content.parts[0].text;
          const fullSuggestion = text + (!text.endsWith(' ') ? ' ' : '') + completion.trimEnd();
          sendResponse({ suggestion: fullSuggestion });
        } else {
          sendResponse({});
        }
      })
      .catch(error => {
        console.error('Error fetching from Gemini API:', error);
        sendResponse({}); // Send empty response on error
      });

    return true; // Indicates an asynchronous response
  }
});