Project Title: Context-Aware AI Autocomplete - A Browser Extension Powered by Gemini and React/TS
1. High-Level Summary

This project is a sophisticated browser extension designed to provide intelligent, context-aware autocomplete suggestions across the entire web. Moving beyond simple word prediction, this extension acts as a smart writing assistant that understands the context of the webpage you are on. By leveraging the power of Google's Gemini large language model, it provides highly relevant, inline "ghost text" suggestions that can be accepted with a single keystroke.

The entire frontend is built on a modern technology stack using React and TypeScript, which ensures a scalable, maintainable, and type-safe codebase. The project is bundled for the browser using Vite, providing an incredibly fast and efficient development experience.

The core user experience is designed to be seamless and intuitive. As a user types in any standard input field or textarea, the extension analyzes the surrounding webpage content and the typed text to generate a meaningful continuation, which appears as faint text directly in front of the cursor.

2. Key Features

Universal Compatibility: The extension is engineered to work on virtually all websites and within most standard <input> and <textarea> fields, providing a consistent experience everywhere you browse.

Inline "Ghost Text" UI: Instead of disruptive dropdowns, suggestions appear fluidly as light-gray text directly after the user's cursor. This provides a non-intrusive and highly efficient user interface.

Context-Aware Predictions: This is the extension's key differentiator. Before requesting a suggestion, it automatically scrapes contextual information from the active webpage (such as the page title and main headings) to provide the Gemini API with the necessary background for generating highly relevant and accurate predictions.

Smart Trigger Logic: Suggestions are not fired on every keystroke, which would be inefficient and costly. Instead, it uses a hybrid approach:

Word Count Trigger: An API call is made immediately after a meaningful phrase is typed (e.g., 4 words) to get faster predictions for longer sentences.

Debounce Trigger: A traditional debounce timer acts as a fallback, making a call only after the user has paused typing, saving API requests for shorter inputs.

Intuitive Interaction: Users can accept a suggestion by simply pressing the Tab or Right Arrow key, seamlessly completing their sentence without moving their hands from the keyboard.

Modern Tech Stack: Built with React, TypeScript, and Vite, the codebase is robust, easy to navigate, and leverages the full power of component-based architecture and static typing.

3. Technical Architecture & How It Works

The extension operates on a decoupled architecture, with clear separation of concerns between its three main parts.

Content Script (content.tsx): The On-Page Controller

Injection & Mounting: When a webpage loads, the content script scans the DOM for all eligible input fields.

React Root Creation: For each input field found, it creates a new container div, wraps the original input field, and uses ReactDOM.createRoot() to mount a dedicated React application into that container.

Component-Based UI: The main React component, <AutocompleteInjector>, takes control. It renders a sophisticated illusion:

A display layer (div with spans) shows the user's actual text and the gray "ghost text" suggestion.

The original <input> element is placed visually on top of this display layer but is made completely transparent (background: transparent, color: transparent). Its blinking cursor (caret-color) remains visible.

Event Hijacking: The user thinks they are typing into a normal field, but they are actually interacting with the now-transparent input that is controlled by React. React state manages its value, and React event handlers (onInput, onKeyDown) capture user actions.

React Application (AutocompleteInjector.tsx): The State and UI Manager

State Management: The component uses useState hooks to manage the userInput and the API suggestion.

Side Effects: A useEffect hook listens for changes in userInput and contains the smart trigger logic (word count and debouncing) to decide when to request a new prediction.

Dynamic Styling: On mount, the component uses window.getComputedStyle() to copy all critical CSS properties (font, padding, border, etc.) from the original input field and applies them to its own container. This ensures the injected component perfectly mimics the website's native design.

Event Handling: The component listens for Tab and ArrowRight key presses to accept suggestions, updating its state and the underlying input's value accordingly.

Background Script (background.ts): The API and Logic Hub

Message Listener: The background script runs persistently and listens for messages from the content script using chrome.runtime.onMessage.

Prompt Engineering: When it receives a request, it doesn't just forward the text. It dynamically constructs a detailed prompt for the Gemini API, combining the user's text with the page context (title, H1) and instructing the AI on its role as an autocomplete assistant.

API Communication: It handles the asynchronous fetch call to the Gemini API, including the API key, payload, and generation configuration (like temperature and max tokens).

Secure & Decoupled: By keeping the API call logic here, the content script never has access to the API key, and the core logic is separated from the DOM manipulation.

4. Technology Stack

Frontend Framework: React 18+

Language: TypeScript

Build Tool: Vite

Core APIs:

Browser Extension APIs: chrome.runtime, chrome.storage for standard extension functionality.

Generative AI: Google Gemini API (via REST endpoint).

Styling: CSS Modules or standard CSS, with dynamic inline styles for mimicry.

Type Definitions: @types/chrome, @types/react.